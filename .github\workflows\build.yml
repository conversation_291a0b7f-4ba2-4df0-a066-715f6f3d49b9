name: Build and Release

on:
  push:
    branches:
      - release
      - main
      - develop
  pull_request:
    branches:
      - release
      - main
      - develop
  workflow_dispatch:

jobs:
  build:
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            platform: linux
          - os: macos-13
            platform: mac
          - os: windows-latest
            platform: windows

    runs-on: ${{ matrix.os }}
    timeout-minutes: 120

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'

      - name: Install Dependencies
        run: npm ci

      - name: Build Vue Application
        run: npm run build-vue

      - name: Package Electron Application
        run: |
          npm install electron-builder --save-dev
          npx electron-builder --${{ matrix.platform }} --publish never
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        timeout-minutes: 60

      - name: Upload Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: novelbox-${{ matrix.platform }}
          path: |
            dist/*.exe
            dist/*.dmg
            dist/*.AppImage
            dist/*.zip
            dist/*.deb
            !dist/*.blockmap
            !dist/*.yml
        timeout-minutes: 20

  release:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/release'
    timeout-minutes: 60
    
    steps:
      - name: Download Artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          files: |
            artifacts/novelbox-windows/dist/*.exe
            artifacts/novelbox-mac/dist/*.dmg
            artifacts/novelbox-linux/dist/*.AppImage
            artifacts/novelbox-windows/dist/*.zip
          draft: true
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}