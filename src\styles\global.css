/* 全局样式和主题变量 */

/* 浅色主题 */
.theme-light {
  --bg-color: #f8f9fa;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --border-color: #dee2e6;
  --sidebar-bg: #ffffff;
  --content-bg: #f8f9fa;
  --input-bg: #ffffff;
  --accent-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --accent-shadow: rgba(102, 126, 234, 0.2);
  --nav-hover-bg: #f1f3f5;
  --nav-active-bg: #e9ecef;
  --nav-active-color: #212529;
  --reset-btn-bg: #f8f9fa;
  --reset-btn-color: #dc3545;
  --reset-btn-hover-bg: #e9ecef;
  --card-bg: #ffffff;
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --card-hover-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --btn-primary-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --btn-primary-color: #ffffff;
  --btn-secondary-bg: #f3f4f6;
  --btn-secondary-color: #374151;
  --btn-danger-bg: #fee2e2;
  --btn-danger-color: #b91c1c;
  --btn-success-bg: #dbeafe;
  --btn-success-color: #1d4ed8;
  --modal-bg: #ffffff;
  --modal-overlay: rgba(0, 0, 0, 0.5);
}

/* 深色主题 */
.theme-dark {
  --bg-color: #1e1e1e;
  --text-primary: #ffffff;
  --text-secondary: #adb5bd;
  --border-color: #495057;
  --sidebar-bg: #2d2d2d;
  --content-bg: #1e1e1e;
  --input-bg: #2d2d2d;
  --accent-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --accent-shadow: rgba(102, 126, 234, 0.3);
  --nav-hover-bg: #3d3d3d;
  --nav-active-bg: #4d4d4d;
  --nav-active-color: #ffffff;
  --reset-btn-bg: #2d2d2d;
  --reset-btn-color: #ff6b6b;
  --reset-btn-hover-bg: #3d3d3d;
  --card-bg: #2d2d2d;
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  --card-hover-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  --btn-primary-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --btn-primary-color: #ffffff;
  --btn-secondary-bg: #3d3d3d;
  --btn-secondary-color: #ffffff;
  --btn-danger-bg: #4d2c2c;
  --btn-danger-color: #ff6b6b;
  --btn-success-bg: #2c3d4d;
  --btn-success-color: #6bafff;
  --modal-bg: #2d2d2d;
  --modal-overlay: rgba(0, 0, 0, 0.7);
}

/* OLED黑主题 */
.theme-oled {
  --bg-color: #000000;
  --text-primary: #ffffff;
  --text-secondary: #adb5bd;
  --border-color: #333333;
  --sidebar-bg: #000000;
  --content-bg: #000000;
  --input-bg: #000000;
  --accent-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --accent-shadow: rgba(102, 126, 234, 0.4);
  --nav-hover-bg: #1a1a1a;
  --nav-active-bg: #333333;
  --nav-active-color: #ffffff;
  --reset-btn-bg: #000000;
  --reset-btn-color: #ff6b6b;
  --reset-btn-hover-bg: #1a1a1a;
  --card-bg: #000000;
  --card-shadow: 0 1px 3px rgba(255, 255, 255, 0.05);
  --card-hover-shadow: 0 4px 6px rgba(255, 255, 255, 0.05);
  --btn-primary-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --btn-primary-color: #ffffff;
  --btn-secondary-bg: #1a1a1a;
  --btn-secondary-color: #ffffff;
  --btn-danger-bg: #2c0000;
  --btn-danger-color: #ff6b6b;
  --btn-success-bg: #001a33;
  --btn-success-color: #6bafff;
  --modal-bg: #000000;
  --modal-overlay: rgba(255, 255, 255, 0.1);
}

/* 蓝色主题 (科技感) */
.theme-blue {
  --bg-color: #e3f2fd;
  --text-primary: #0d47a1;
  --text-secondary: #1565c0;
  --border-color: #90caf9;
  --sidebar-bg: #bbdefb;
  --content-bg: #e3f2fd;
  --input-bg: #ffffff;
  --accent-color: linear-gradient(135deg, #2196f3 0%, #0d47a1 100%);
  --accent-shadow: rgba(33, 150, 243, 0.3);
  --nav-hover-bg: #90caf9;
  --nav-active-bg: #64b5f6;
  --nav-active-color: #0d47a1;
  --reset-btn-bg: #e3f2fd;
  --reset-btn-color: #d32f2f;
  --reset-btn-hover-bg: #bbdefb;
  --card-bg: #ffffff;
  --card-shadow: 0 1px 3px rgba(33, 150, 243, 0.2);
  --card-hover-shadow: 0 4px 6px rgba(33, 150, 243, 0.3);
  --btn-primary-bg: linear-gradient(135deg, #2196f3 0%, #0d47a1 100%);
  --btn-primary-color: #ffffff;
  --btn-secondary-bg: #bbdefb;
  --btn-secondary-color: #0d47a1;
  --btn-danger-bg: #ffcdd2;
  --btn-danger-color: #c62828;
  --btn-success-bg: #c8e6c9;
  --btn-success-color: #2e7d32;
  --modal-bg: #ffffff;
  --modal-overlay: rgba(13, 71, 161, 0.5);
}

/* 绿色主题 (自然清新) */
.theme-green {
  --bg-color: #e8f5e9;
  --text-primary: #1b5e20;
  --text-secondary: #2e7d32;
  --border-color: #a5d6a7;
  --sidebar-bg: #c8e6c9;
  --content-bg: #e8f5e9;
  --input-bg: #ffffff;
  --accent-color: linear-gradient(135deg, #4caf50 0%, #1b5e20 100%);
  --accent-shadow: rgba(76, 175, 80, 0.3);
  --nav-hover-bg: #a5d6a7;
  --nav-active-bg: #81c784;
  --nav-active-color: #1b5e20;
  --reset-btn-bg: #e8f5e9;
  --reset-btn-color: #d32f2f;
  --reset-btn-hover-bg: #c8e6c9;
  --card-bg: #ffffff;
  --card-shadow: 0 1px 3px rgba(76, 175, 80, 0.2);
  --card-hover-shadow: 0 4px 6px rgba(76, 175, 80, 0.3);
  --btn-primary-bg: linear-gradient(135deg, #4caf50 0%, #1b5e20 100%);
  --btn-primary-color: #ffffff;
  --btn-secondary-bg: #c8e6c9;
  --btn-secondary-color: #1b5e20;
  --btn-danger-bg: #ffcdd2;
  --btn-danger-color: #c62828;
  --btn-success-bg: #c8e6c9;
  --btn-success-color: #2e7d32;
  --modal-bg: #ffffff;
  --modal-overlay: rgba(27, 94, 32, 0.5);
}

/* 紫色主题 (优雅神秘) */
.theme-purple {
  --bg-color: #f3e5f5;
  --text-primary: #4a148c;
  --text-secondary: #6a1b9a;
  --border-color: #ce93d8;
  --sidebar-bg: #e1bee7;
  --content-bg: #f3e5f5;
  --input-bg: #ffffff;
  --accent-color: linear-gradient(135deg, #9c27b0 0%, #4a148c 100%);
  --accent-shadow: rgba(156, 39, 176, 0.3);
  --nav-hover-bg: #ce93d8;
  --nav-active-bg: #ba68c8;
  --nav-active-color: #4a148c;
  --reset-btn-bg: #f3e5f5;
  --reset-btn-color: #d32f2f;
  --reset-btn-hover-bg: #e1bee7;
  --card-bg: #ffffff;
  --card-shadow: 0 1px 3px rgba(156, 39, 176, 0.2);
  --card-hover-shadow: 0 4px 6px rgba(156, 39, 176, 0.3);
  --btn-primary-bg: linear-gradient(135deg, #9c27b0 0%, #4a148c 100%);
  --btn-primary-color: #ffffff;
  --btn-secondary-bg: #e1bee7;
  --btn-secondary-color: #4a148c;
  --btn-danger-bg: #ffcdd2;
  --btn-danger-color: #c62828;
  --btn-success-bg: #c8e6c9;
  --btn-success-color: #2e7d32;
  --modal-bg: #ffffff;
  --modal-overlay: rgba(74, 20, 140, 0.5);
}

/* 默认使用浅色主题 */
:root {
  --bg-color: #f8f9fa;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --border-color: #dee2e6;
  --sidebar-bg: #ffffff;
  --content-bg: #f8f9fa;
  --input-bg: #ffffff;
  --accent-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --accent-shadow: rgba(102, 126, 234, 0.2);
  --nav-hover-bg: #f1f3f5;
  --nav-active-bg: #e9ecef;
  --nav-active-color: #212529;
  --reset-btn-bg: #f8f9fa;
  --reset-btn-color: #dc3545;
  --reset-btn-hover-bg: #e9ecef;
  --card-bg: #ffffff;
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --card-hover-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --btn-primary-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --btn-primary-color: #ffffff;
  --btn-secondary-bg: #f3f4f6;
  --btn-secondary-color: #374151;
  --btn-danger-bg: #fee2e2;
  --btn-danger-color: #b91c1c;
  --btn-success-bg: #dbeafe;
  --btn-success-color: #1d4ed8;
  --modal-bg: #ffffff;
  --modal-overlay: rgba(0, 0, 0, 0.5);
}

/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--bg-color);
  color: var(--text-primary);
  transition: background-color 0.3s, color 0.3s;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 在深色和OLED主题中调整滚动条样式 */
.theme-dark ::-webkit-scrollbar-track,
.theme-oled ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.theme-dark ::-webkit-scrollbar-thumb,
.theme-oled ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.theme-dark ::-webkit-scrollbar-thumb:hover,
.theme-oled ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Utility classes */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active, .slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-enter-active, .slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-right-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

/* Focus outline styles */
*:focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background: rgba(102, 126, 234, 0.2);
  color: inherit;
}

/* Button reset */
button {
  font-family: inherit;
  cursor: pointer;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Input and textarea reset */
input, textarea {
  font-family: inherit;
}

/* Text selection in inputs */
input:focus, textarea:focus {
  outline: none;
}

/* Prevent text selection on UI elements */
.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Loading animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Common button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.9em;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  background: transparent;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #f1f3f4;
  color: #5f6368;
  border: 1px solid #dadce0;
}

.btn-secondary:hover:not(:disabled) {
  background: #e8eaed;
  border-color: #c4c7ca;
}

.btn-danger {
  background: #ff4757;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #ff3742;
  transform: translateY(-1px);
}

/* Form styles */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #555;
  margin-bottom: 8px;
  font-size: 0.9em;
}

.form-label.required::after {
  content: ' *';
  color: #ff4757;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1em;
  transition: border-color 0.2s, box-shadow 0.2s;
  background: white;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input:invalid, .form-textarea:invalid {
  border-color: #ff4757;
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
}

/* Card styles */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* Modal overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  /* Dark mode styles can be added here */
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    border: 2px solid;
  }
  
  .card {
    border: 1px solid #333;
  }
}